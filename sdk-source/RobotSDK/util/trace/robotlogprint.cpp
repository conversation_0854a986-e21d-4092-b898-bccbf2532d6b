#include "robotlogprint.h"
#include <stdio.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <time.h>
#include <string.h>
#include <stdarg.h>

#include "AuboRobotMetaType.h"

// 默认的全局日志实例，用于向后兼容
static RobotLogPrint* g_defaultLogPrint = NULL;
static pthread_mutex_t g_defaultLogMutex = PTHREAD_MUTEX_INITIALIZER;

RobotLogPrint::RobotLogPrint()
{
    m_logPrintCallback = NULL;

    m_arg = NULL;

    memset(m_dataBuf, 0, sizeof (m_dataBuf));

    pthread_mutex_init(&m_logPrintMutex, NULL);

    // 如果还没有默认日志实例，将第一个创建的实例设为默认
    pthread_mutex_lock(&g_defaultLogMutex);
    if (g_defaultLogPrint == NULL) {
        g_defaultLogPrint = this;
    }
    pthread_mutex_unlock(&g_defaultLogMutex);
}

RobotLogPrint::~RobotLogPrint()
{
    // 如果当前实例是默认日志实例，清除默认指针
    pthread_mutex_lock(&g_defaultLogMutex);
    if (g_defaultLogPrint == this) {
        g_defaultLogPrint = NULL;
    }
    pthread_mutex_unlock(&g_defaultLogMutex);

    pthread_mutex_destroy(&m_logPrintMutex);
}

void RobotLogPrint::printTrace(int level, const char *format, ...)
{
    pthread_mutex_lock(&m_logPrintMutex);

    memset(m_dataBuf, 0, sizeof (m_dataBuf));

    va_list ap;
    va_start(ap, format);
    vsnprintf(m_dataBuf, MAX_BUF, format, ap);
    va_end(ap);

    if(m_logPrintCallback != NULL)
    {
        m_logPrintCallback((aubo_robot_namespace::LOG_LEVEL)level, m_dataBuf, m_arg);
    }
    else
    {
        printf("%s\n", m_dataBuf);
    }

    pthread_mutex_unlock(&m_logPrintMutex);
}

void RobotLogPrint::registerRobotLogPrintCallback(LogPrintCallback callback, void *arg)
{
    pthread_mutex_lock(&m_logPrintMutex);
    m_logPrintCallback = callback;
    m_arg              = arg;
    pthread_mutex_unlock(&m_logPrintMutex);
}

RobotLogPrint *RobotLogPrint::getRobotLogPrintPtr()
{
    pthread_mutex_lock(&g_defaultLogMutex);
    RobotLogPrint* result = g_defaultLogPrint;
    pthread_mutex_unlock(&g_defaultLogMutex);
    return result;
}
