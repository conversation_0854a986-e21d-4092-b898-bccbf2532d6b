#include "robotioservice.h"

#include <string.h>
#include "globalutil.h"
#include "robotcontrolservices.h"

#if defined(__WIN32__) || defined (WIN32)
#define _MSWSOCK_
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#endif

using namespace std;
using namespace aubo_robot_namespace;
using namespace aubo_robot_logtrace;

#define INVALID_IO_VALUE (~0x00)

RobotIoService::RobotIoService(RobotControlServices *p)
{
    m_robotBaseService = p;

    initToolIoConfig();

    initBoardIoConfig();
}


/** 获取接口板的配置 **/
int RobotIoService::getBoardIOConfig(const vector<RobotIoType> &typeVector, vector<RobotIoDesc> &configVector)
{
    int ret = ErrnoSucc;

    configVector.clear();

    for(int i=0; i<(int)typeVector.size(); i++)
    {
        RobotIoType type = typeVector[i];

        if(m_boardIoTypeSet.count(type) > 0 )       //判断IO类型是否存在
        {
            map<string, int> ioNameAddrMap = m_boardIOMaping[type];

            for(map<string,int>::iterator it=ioNameAddrMap.begin(); it!=ioNameAddrMap.end(); ++it)
            {
                RobotIoDesc robotIoDesc;

                initRobotIoDesc(robotIoDesc, (it->first)/*名称*/, (RobotIoType)type/*类型*/, (it->first)/*ID*/, it->second/*地址*/, INVALID_IO_VALUE/*数据*/);   //名称和ID共用

                configVector.push_back(robotIoDesc);
            }
        }
        else
        {
            ret = ErrCode_ParamError;

            configVector.clear();

            W_ERROR("sdk log: Call getBoardIOConfig IO type error.");

            break;
        }
    }

    return ret;
}



int RobotIoService::getBoardIOStatus(const vector<RobotIoType> typeVector, vector<RobotIoDesc> &statusVector)
{
    int ret = ErrnoSucc;

    std::vector<RobotDiagnosisIODesc> diStatusVector;
    std::vector<RobotDiagnosisIODesc> doStatusVector;
    std::vector<RobotAnalogIODesc>    aiStatusVector;
    std::vector<RobotAnalogIODesc>    aoStatusVector;

    statusVector.clear();

    /** 获取接口板所有IO的数据　　注:IO数据存储在vector中是按照地址为索引存储的**/
    if( ( (ret = m_robotBaseService->getInterfaceBoardAllDIStatusService(diStatusVector))==ErrnoSucc) &&
            ( (ret = m_robotBaseService->getInterfaceBoardAllDOStatusService(doStatusVector))==ErrnoSucc) &&
            ( (ret = m_robotBaseService->getInterfaceBoardAllAIStatusService(aiStatusVector))==ErrnoSucc) &&
            ( (ret = m_robotBaseService->getInterfaceBoardAllAOStatusService(aoStatusVector))==ErrnoSucc) )
    {

        for(int i=0; i<(int)typeVector.size(); i++)
        {
            RobotIoType type = typeVector[i];      //IO类型

            if(m_boardIoTypeSet.count(type) >0 )   //判断IO类型存在
            {
                map<string, int> ioNameAddrMap = m_boardIOMaping[type];

                for(map<string,int>::iterator it=ioNameAddrMap.begin(); it!=ioNameAddrMap.end(); ++it)
                {
                    RobotIoDesc robotIoDesc;

                    initRobotIoDesc(robotIoDesc, (it->first)/*名称*/, (RobotIoType)type/*类型*/, (it->first)/*ID*/, it->second/*地址*/, INVALID_IO_VALUE/*数据*/);

                    switch(type)
                    {
                    /** 数字量输入　**/
                    case RobotBoardUserDI:
                    case RobotBoardControllerDI:

                        if( robotIoDesc.ioAddr < (int)diStatusVector.size())
                        {
                            robotIoDesc.ioValue = diStatusVector[robotIoDesc.ioAddr].value;
                        }
                        else
                        {
                            robotIoDesc.ioValue = INVALID_IO_VALUE;
                        }
                        break;

                        /** 数字量输出　**/
                    case RobotBoardControllerDO:
                    case RobotBoardUserDO:
                        if( robotIoDesc.ioAddr < (int)doStatusVector.size())
                        {
                            robotIoDesc.ioValue = doStatusVector[robotIoDesc.ioAddr].value;
                        }
                        else
                        {
                            robotIoDesc.ioValue = INVALID_IO_VALUE;
                        }
                        break;

                        /** 模拟量输入　**/
                    case RobotBoardControllerAI:
                    case RobotBoardUserAI:
                        if( robotIoDesc.ioAddr < (int)aiStatusVector.size())
                        {
                            robotIoDesc.ioValue = aiStatusVector[robotIoDesc.ioAddr].value;
                        }
                        else
                        {
                            robotIoDesc.ioValue = INVALID_IO_VALUE;
                        }
                        break;

                        /** 模拟量输出　**/
                    case RobotBoardControllerAO:
                    case RobotBoardUserAO:
                        if( robotIoDesc.ioAddr < (int)aoStatusVector.size())
                        {
                            robotIoDesc.ioValue = aoStatusVector[robotIoDesc.ioAddr].value;
                        }
                        else
                        {
                            robotIoDesc.ioValue = INVALID_IO_VALUE;
                        }
                        break;

                    default:
                        break;
                    }


                    statusVector.push_back(robotIoDesc);
                }
            }
            else
            {
                ret = ErrCode_ParamError;

                W_ERROR("sdk log: Call getBoardIOStatus RobotIoType not Exist.");

                break;
            }
        }
    }


    return ret;
}


int RobotIoService::getBoardIOStatus(RobotIoType type, string name, double &value)
{
    int ret = ErrnoSucc;

    bool isTypeNameMapError = true;

    /** 判断类型和名称是否匹配 **/
    if(m_boardIoTypeSet.count(type) > 0 )
    {
        if( m_boardIOMaping[type].count(name) > 0 )
        {
            isTypeNameMapError = false;
        }
    }

    if(isTypeNameMapError == false)
    {
        int addr = m_boardIOMaping[type][name];    //取得地址

        ret = getBoardIOStatus(type, addr, value);
    }
    else
    {
        ret = ErrCode_ParamError;

        W_ERROR("sdk log: Call getBoardIOStatus, type name mismatch.");
    }

    return ret;
}


int RobotIoService::getBoardIOStatus(RobotIoType type, int addr, double &value)
{
    int ret = ErrnoSucc;

    IO_STATUS  didoValue;

    bool isTypeNameMapError = true;

    if(m_boardIOTypeAddrSetMaping.count(type) > 0 )
    {
        if(m_boardIOTypeAddrSetMaping[type].count(addr) > 0)
        {
            isTypeNameMapError = false;
        }
    }

    if(isTypeNameMapError == false)
    {
        switch(type)
        {
        case RobotBoardUserDI:
        case RobotBoardControllerDI:
            if( (ret = m_robotBaseService->getInterfaceBoardDIStatusService(addr,didoValue)) == ErrnoSucc)
            {
                value = didoValue;
            }
            break;

        case RobotBoardControllerDO:
        case RobotBoardUserDO:
            if( (ret = m_robotBaseService->getInterfaceBoardDOStatusService(addr,didoValue)) == ErrnoSucc)
            {
                value = didoValue;
            }
            break;

        case RobotBoardUserAI:
            ret = m_robotBaseService->getInterfaceBoardAIStatusService(addr,value);
            break;

        case RobotBoardUserAO:
            ret = m_robotBaseService->getInterfaceBoardAOStatusService(addr,value);
            break;

        default:
            break;
        }
    }
    else
    {
        ret = ErrCode_ParamError;

        W_ERROR("sdk log:  Call getBoardIOStatus, type addr mismatch.");
    }

    return ret;
}


int  RobotIoService::setBoardIOStatus(RobotIoType type, string name, double value)
{
    int ret = ErrnoSucc;

    if(type == RobotBoardUserDO || type == RobotBoardControllerDO || type == RobotBoardUserAO || type == RobotBoardControllerAO)
    {
        if( m_boardIOMaping[type].count(name) > 0 )
        {
            int addr = m_boardIOMaping[type][name];

            ret = setBoardIOStatus( type, addr, value);
        }
        else
        {
            ret = ErrCode_ParamError;

            W_ERROR("sdk log: Call setBoardIOStatus, type addr mismatch.");
        }
    }
    else
    {
        ret = ErrCode_ParamError;

        W_ERROR("sdk log: Call setBoardIOStatus, type not exist.");
    }

    return ret;
}


int  RobotIoService::setBoardIOStatus(RobotIoType type, int addr, double value)
{
    int ret = ErrnoSucc;

    if(type == RobotBoardUserDO || type == RobotBoardControllerDO)
    {
        if(m_boardIOTypeAddrSetMaping[type].count(addr) > 0 )
        {
            ret = m_robotBaseService->setInterfaceBoardDOStatusService(addr, (value>0.5)? IO_STATUS_VALID:IO_STATUS_INVALID);
        }
        else
        {
            ret = ErrCode_ParamError;

            W_ERROR("sdk log: Call setBoardIOStatusByaddr DO, type addr mismatch.");
        }
    }
    else if(type == RobotBoardControllerAO || type == RobotBoardUserAO)
    {
        if(m_boardIOTypeAddrSetMaping[type].count(addr) > 0 )
        {
            ret = m_robotBaseService->setInterfaceBoardAOStatusService(addr, value);
        }
        else
        {
            ret = ErrCode_ParamError;

            W_ERROR("sdk log: Call setBoardIOStatusByaddr AO,  type addr mismatch.");
        }
    }
    else
    {
        ret = ErrCode_ParamError;

        W_ERROR("sdk log: Call setBoardIOStatusByaddr, type invalid.");
    }

    return ret;
}








/**
 * 设置工具端电源电压类型
 *
 * 150ms 最多重试3次
 *
 **/
int RobotIoService::setToolPowerVoltageType(ToolPowerType type)
{
    int ret = m_robotBaseService->setToolPowerVoltageTypeService(type);

    if(ret != ErrnoSucc)
    {
        W_ERROR("sdk log: Setting tool voltage type failed. ret = %d", ret);

        return ret;
    }

    struct timeval timeout;

    ToolPowerType realPowerType;

    for(int i = 0; i<3 ;i++)
    {
        timeout.tv_sec  = 0;
        timeout.tv_usec = 150*1000;

#if defined(__WIN32__) || defined (WIN32)

    // 每次调用都创建新的socket，避免静态变量问题
    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeout);
    closesocket(sock);

#else

    select(0,NULL,NULL,NULL,&timeout);

#endif

        ret = getToolPowerVoltageType(realPowerType);

        if(ret != ErrnoSucc)
        {
            W_WARN("sdk log: get tool voltage type failed, number times:%d", i+1);

            continue;
        }

        if( realPowerType == type)
        {
            return ErrnoSucc;
        }
        else
        {
            W_WARN("sdk log: Real-time tool voltage type does not match expectations, number times:%d", i+1);

            continue;
        }
    }

    return ErrCode_Failed;
}

/**
 * 获取工具端电源电压类型
 **/
int  RobotIoService::getToolPowerVoltageType(ToolPowerType &type)
{   
    return m_robotBaseService->getToolPowerVoltageTypeService(type);
}




/** 设置工具端数字量类型  DI或者DO **/
int RobotIoService::setToolDigitalIOType(ToolDigitalIOAddr addr, ToolIOType type)
{
    int ret = ErrnoSucc;

    if(m_toolDiagnosisIOAddrSet.count(addr) > 0)
    {
       ret = m_robotBaseService->setToolDigitalIOTypeService(addr, type);
    }
    else
    {
        W_ERROR("sdk log: setToolDigitalIOType addr error.");

        return ErrCode_ParamError;
    }

    if(ret != ErrnoSucc)
    {
        W_ERROR("sdk log: Setting tool digital io type failed. ret = %d", ret);

        return ret;
    }

    struct timeval timeout;

    std::vector<aubo_robot_namespace::RobotIoDesc> statusVector;

    for(int i = 0; i<3 ;i++)
    {
        timeout.tv_sec  = 0;
        timeout.tv_usec = 150*1000;

#if defined(__WIN32__) || defined (WIN32)

    // 每次调用都创建新的socket，避免静态变量问题
    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeout);
    closesocket(sock);

#else

    select(0,NULL,NULL,NULL,&timeout);

#endif

        statusVector.clear();
        ret = getAllToolDigitalIOStatus(statusVector);
        if(ret != ErrnoSucc)
        {
            W_WARN("sdk log: get tool digital io type failed, number times:%d", i+1);

            continue;
        }

        if(statusVector.size()>=4 && statusVector[addr].ioType-RobotToolDI == type)
        {
            return ErrnoSucc;
        }
        else
        {
            W_WARN("sdk log: Real-time tool digital io type does not match expectations, number times:%d", i+1);

            W_WARN("sdk log: Current state: io%d-%d", addr, statusVector[addr].ioType-RobotToolDI);

            W_WARN("sdk log: target  state: io%d-%d", addr, type);

            continue;
        }
    }

    return ErrCode_Failed;
}

/** 获取工具端电源电压状态 **/
int RobotIoService::getToolPowerVoltageStatus(double &value)
{
    return m_robotBaseService->getToolPowerVoltageStatusService(value);
}

/** IO配置:配置工具端IO的电源电压类型和IO类型 **/
int RobotIoService::setToolPowerTypeAndDigitalIOType(ToolPowerType type, ToolIOType io0, ToolIOType io1, ToolIOType io2, ToolIOType io3)
{
    int ret = m_robotBaseService->setToolPowerTypeAndDigitalIOTypeService(type, io0, io1, io2, io3);

    if(ret != ErrnoSucc)
    {
        W_ERROR("sdk log: Setting tool voltage type and digital io type failed. ret = %d", ret);

        return ret;
    }

    struct timeval timeout;

    ToolPowerType realPowerType;

    std::vector<aubo_robot_namespace::RobotIoDesc> statusVector;

    for(int i = 0; i<3 ;i++)
    {
        timeout.tv_sec  = 0;
        timeout.tv_usec = 150*1000;

#if defined(__WIN32__) || defined (WIN32)

    // 每次调用都创建新的socket，避免静态变量问题
    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeout);
    closesocket(sock);

#else

    select(0,NULL,NULL,NULL,&timeout);

#endif

        ret = getToolPowerVoltageType(realPowerType);

        if(ret != ErrnoSucc)
        {
            W_WARN("sdk log: get tool voltage type failed, number times:%d", i+1);

            continue;
        }

        if( realPowerType != type)
        {
            W_WARN("sdk log: Real-time tool voltage type does not match expectations, number times:%d", i+1);

            continue;
        }

        statusVector.clear();
        ret = getAllToolDigitalIOStatus(statusVector);
        if(ret != ErrnoSucc)
        {
            W_WARN("sdk log: get tool digital io type failed, number times:%d", i+1);

            continue;
        }

        if(statusVector.size()>=4 &&
           statusVector[0].ioType-RobotToolDI == io0 &&
           statusVector[1].ioType-RobotToolDI == io1 &&
           statusVector[2].ioType-RobotToolDI == io2 &&
           statusVector[3].ioType-RobotToolDI == io3)
        {
            return ErrnoSucc;
        }
        else
        {
            W_WARN("sdk log: Real-time tool digital io type does not match expectations, number times:%d", i+1);

            W_WARN("sdk log: Current state: io0-%d  io1-%d  io2-%d  io3-%d", statusVector[0].ioType-RobotToolDI, statusVector[1].ioType-RobotToolDI, statusVector[2].ioType-RobotToolDI, statusVector[3].ioType-RobotToolDI);

            W_WARN("sdk log: target  state: io0-%d  io1-%d  io2-%d  io3-%d", io0, io1, io2, io3);

            continue;
        }
    }

    return ErrCode_Failed;
}


/** 获取工具端所有数字量IO状态 **/
int RobotIoService::getAllToolDigitalIOStatus(std::vector<RobotIoDesc> &statusVector)
{
    int ret = ErrnoSucc;

    std::vector<RobotDiagnosisIODesc> diagnosisIOStatusVector;

    statusVector.clear();

    ret = m_robotBaseService->getToolAllDigitalIOStatusService(diagnosisIOStatusVector);

    if(ret == ErrnoSucc)
    {
        for(int i=0;i<diagnosisIOStatusVector.size();i++)
        {
            if( m_toolDiagnosisIOAddrNameMaping.count(diagnosisIOStatusVector[i].addr) > 0 )
            {
                RobotIoDesc robotIoDesc;

                memset(&robotIoDesc, 0, sizeof(RobotIoDesc));

                initRobotIoDesc(robotIoDesc, m_toolDiagnosisIOAddrNameMaping[diagnosisIOStatusVector[i].addr]/*名称*/,
                                             (diagnosisIOStatusVector[i].type == IO_IN)? RobotToolDI:RobotToolDO/*类型*/,
                                             m_toolDiagnosisIOAddrNameMaping[diagnosisIOStatusVector[i].addr]/*ID*/,
                                             diagnosisIOStatusVector[i].addr/*地址*/,
                                             diagnosisIOStatusVector[i].value/*数据*/);

                statusVector.push_back(robotIoDesc);
            }
        }
    }

    return ret;
}


/** 获取工具端所有AI状态  **/
int RobotIoService::getAllToolAIStatus(std::vector<RobotIoDesc> &statusVector)
{
    size_t i = 0;

    int ret = ErrnoSucc;

    std::vector<RobotAnalogIODesc> analogIOStatusVector;

    statusVector.clear();

    ret = m_robotBaseService->getToolAllAIStatusService(analogIOStatusVector);   //获取所有AI状态　　注:IO数据存储在vector中是按照地址为索引存储的

    if(ret == ErrnoSucc)
    {
        for(map<string,int>::iterator it=m_toolAIMaping.begin(); it!=m_toolAIMaping.end(); ++it)
        {
            int addr = (it->second);               //地址

            if(addr < (int)analogIOStatusVector.size())
            {
                RobotIoDesc robotIoDesc;

                memset(&robotIoDesc, 0, sizeof(RobotIoDesc));

                initRobotIoDesc(robotIoDesc, (it->first)/*名称*/, RobotToolAI/*类型*/, (it->first)/*ID*/, analogIOStatusVector[i].addr/*地址*/, analogIOStatusVector[addr].value/*数据*/);

                statusVector.push_back(robotIoDesc);
            }
        }
    }

    return ret;
}


/** 通过名称获取工具端IO的状态 **/
int RobotIoService::getToolIoStatusByName(string name, double &value)
{
    size_t  addr = 0;

    int ret = ErrnoSucc;

    if(m_toolDiagnosisIONameAddrMaping.count(name) > 0)
    {
        addr = m_toolDiagnosisIONameAddrMaping[name];     //取得名称对应的地址

        std::vector<RobotIoDesc> statusVector;

        if(ErrnoSucc == (ret = getAllToolDigitalIOStatus(statusVector)) && statusVector.size() > addr)
        {
            value = statusVector[addr].ioValue;
        }
    }
    else if(m_toolAIMaping.count(name) > 0)
    {
        addr = m_toolAIMaping[name];              //取得名称对应的地址

        std::vector<RobotIoDesc> statusVector;

        if(ErrnoSucc == (ret =getAllToolAIStatus(statusVector)) && statusVector.size() > addr)
        {
            value = statusVector[addr].ioValue;

            ret = ErrnoSucc;
        }
    }
    else
    {
        W_ERROR("sdk log: Call getToolIoStatus name error.");

        ret = ErrCode_ParamError;
    }

    return ret;
}

/** 设置工具端DO的状态 通过地址 **/
int RobotIoService::setToolDOStatus(ToolDigitalIOAddr addr, IO_STATUS value)
{
    return  m_robotBaseService->setToolDigitalIOStatusService(addr, value);
}

/** 设置工具端DO的状态 通过名称 **/
int RobotIoService::setToolDOStatus(string name, IO_STATUS value)
{
    int ret = ErrnoSucc;

    if(m_toolDiagnosisIONameAddrMaping.count(name) > 0)
    {
        int addr = m_toolDiagnosisIONameAddrMaping[name];

        ret = setToolDOStatus((ToolDigitalIOAddr)addr, value);
    }
    else
    {
        ret = ErrCode_ParamError;

        W_ERROR("sdk log: setToolDOStatus name not exist.");
    }

    return ret;
}


/** 初始化RobotIoDesc 结构体 ***/
void RobotIoService::initRobotIoDesc(RobotIoDesc &robotIoDesc, const string &ioId, RobotIoType ioType, const string &ioName, int ioAddr, double ioValue)
{
    memset(&robotIoDesc, 0, sizeof(robotIoDesc));

    memcpy(robotIoDesc.ioId,  ioId.c_str(), (ioId.length()>31)? 31:ioId.length() );
    memcpy(robotIoDesc.ioName,  ioName.c_str(), (ioName.length()>31)? 31:ioName.length() );
    robotIoDesc.ioType = ioType;
    robotIoDesc.ioAddr = ioAddr;
    robotIoDesc.ioValue= ioValue;
}


void RobotIoService::initBoardIoConfig()
{
    /**
     * 接口板IO分为控制器IO和用户IO两大类
     *   控制器IO包括: ControllerDI   ControllerDO  ControllerAI ControllerAO
     *   用户IO包括:   UserDI  UserDO  UserAI  UserAO
     **/

    //接口板名称地址映射关系
    map<string, int>  boardControllerDINameAddrMap;
    map<string, int>  boardControllerDONameAddrMap;
    map<string, int>  boardControllerAINameAddrMap;
    map<string, int>  boardControllerAONameAddrMap;

    map<string, int>  boardUserDINameAddrMap;
    map<string, int>  boardUserDONameAddrMap;
    map<string, int>  boardUserAINameAddrMap;
    map<string, int>  boardUserAONameAddrMap;


    /** 接口板 DI **/
    //Controller IO
    boardControllerDINameAddrMap.clear();
    boardControllerDINameAddrMap["SI00"] = 0X00;
    boardControllerDINameAddrMap["SI01"] = 0X01;
    boardControllerDINameAddrMap["SI02"] = 0X02;
    boardControllerDINameAddrMap["SI03"] = 0X03;
    boardControllerDINameAddrMap["SI04"] = 0X04;
    boardControllerDINameAddrMap["SI05"] = 0X05;
    boardControllerDINameAddrMap["SI06"] = 0X06;
    boardControllerDINameAddrMap["SI07"] = 0X07;
    boardControllerDINameAddrMap["SI10"] = 0X08;
    boardControllerDINameAddrMap["SI11"] = 0X09;
    boardControllerDINameAddrMap["SI12"] = 0X0A;
    boardControllerDINameAddrMap["SI13"] = 0X0B;
    boardControllerDINameAddrMap["SI14"] = 0X0C;
    boardControllerDINameAddrMap["SI15"] = 0X0D;
    boardControllerDINameAddrMap["SI16"] = 0X0E;
    boardControllerDINameAddrMap["SI17"] = 0X0F;  //Safe IO DI
    boardControllerDINameAddrMap["CI00"] = 0X10;
    boardControllerDINameAddrMap["CI01"] = 0X11;
    boardControllerDINameAddrMap["CI02"] = 0X12;
    boardControllerDINameAddrMap["CI03"] = 0X13;
    boardControllerDINameAddrMap["CI10"] = 0X14;
    boardControllerDINameAddrMap["CI11"] = 0X15;
    boardControllerDINameAddrMap["CI12"] = 0X16;
    boardControllerDINameAddrMap["CI13"] = 0X17;  //Internal IO   DI
    boardControllerDINameAddrMap["LI00"] = 0X18;
    boardControllerDINameAddrMap["LI01"] = 0X19;
    boardControllerDINameAddrMap["LI02"] = 0X1A;
    boardControllerDINameAddrMap["LI03"] = 0X1B;
    boardControllerDINameAddrMap["LI04"] = 0X1C;
    boardControllerDINameAddrMap["LI05"] = 0X1D;  //Linkage IO   DI


    //接口板用户DI
    boardUserDINameAddrMap.clear();
    boardUserDINameAddrMap["F1"]         = 0X1E;
    boardUserDINameAddrMap["F2"]         = 0X1F;
    boardUserDINameAddrMap["F3"]         = 0X20;
    boardUserDINameAddrMap["F4"]         = 0X21;
    boardUserDINameAddrMap["F5"]         = 0X22;
    boardUserDINameAddrMap["F6"]         = 0X23;  //Default project IO   DI
    boardUserDINameAddrMap["U_DI_00"]    = 0X24;
    boardUserDINameAddrMap["U_DI_01"]    = 0X25;
    boardUserDINameAddrMap["U_DI_02"]    = 0X26;
    boardUserDINameAddrMap["U_DI_03"]    = 0X27;
    boardUserDINameAddrMap["U_DI_04"]    = 0X28;
    boardUserDINameAddrMap["U_DI_05"]    = 0X29;
    boardUserDINameAddrMap["U_DI_06"]    = 0X2A;
    boardUserDINameAddrMap["U_DI_07"]    = 0X2B;
    boardUserDINameAddrMap["U_DI_10"]    = 0X2C;
    boardUserDINameAddrMap["U_DI_11"]    = 0X2D;
    boardUserDINameAddrMap["U_DI_12"]    = 0X2E;
    boardUserDINameAddrMap["U_DI_13"]    = 0X2F;
    boardUserDINameAddrMap["U_DI_14"]    = 0X30;
    boardUserDINameAddrMap["U_DI_15"]    = 0X31;
    boardUserDINameAddrMap["U_DI_16"]    = 0X32;
    boardUserDINameAddrMap["U_DI_17"]    = 0X33;  //UserIO  DI







    /** 接口板 DO **/
    //Controller IO
    boardControllerDONameAddrMap.clear();
    boardControllerDONameAddrMap["SO00"] = 0X00;
    boardControllerDONameAddrMap["SO01"] = 0X01;
    boardControllerDONameAddrMap["SO02"] = 0X02;
    boardControllerDONameAddrMap["SO03"] = 0X03;
    boardControllerDONameAddrMap["SO04"] = 0X04;
    boardControllerDONameAddrMap["SO05"] = 0X05;
    boardControllerDONameAddrMap["SO06"] = 0X06;
    boardControllerDONameAddrMap["SO07"] = 0X07;
    boardControllerDONameAddrMap["SO10"] = 0X08;
    boardControllerDONameAddrMap["SO11"] = 0X09;
    boardControllerDONameAddrMap["SO12"] = 0X0A;
    boardControllerDONameAddrMap["SO13"] = 0X0B;
    boardControllerDONameAddrMap["SO14"] = 0X0C;
    boardControllerDONameAddrMap["SO15"] = 0X0D;
    boardControllerDONameAddrMap["SO16"] = 0X0E;
    boardControllerDONameAddrMap["SO17"] = 0X0F;   //Safe IO  DO
    boardControllerDONameAddrMap["CO00"] = 0X10;
    boardControllerDONameAddrMap["CO01"] = 0X11;
    boardControllerDONameAddrMap["CO02"] = 0X12;
    boardControllerDONameAddrMap["CO03"] = 0X13;
    boardControllerDONameAddrMap["CO10"] = 0X14;
    boardControllerDONameAddrMap["CO11"] = 0X15;
    boardControllerDONameAddrMap["CO12"] = 0X16;
    boardControllerDONameAddrMap["CO13"] = 0X17;  //Internal IO  DO
    boardControllerDONameAddrMap["LO00"] = 0X18;
    boardControllerDONameAddrMap["LO01"] = 0X19;
    boardControllerDONameAddrMap["LO02"] = 0X1A;
    boardControllerDONameAddrMap["LO03"] = 0X1B;  //Linkage IO  DO

    //接口板用户DO
    boardUserDONameAddrMap.clear();
    boardUserDONameAddrMap["U_DO_00"]    = 0X20;
    boardUserDONameAddrMap["U_DO_01"]    = 0X21;
    boardUserDONameAddrMap["U_DO_02"]    = 0X22;
    boardUserDONameAddrMap["U_DO_03"]    = 0X23;
    boardUserDONameAddrMap["U_DO_04"]    = 0X24;
    boardUserDONameAddrMap["U_DO_05"]    = 0X25;
    boardUserDONameAddrMap["U_DO_06"]    = 0X26;
    boardUserDONameAddrMap["U_DO_07"]    = 0X27;
    boardUserDONameAddrMap["U_DO_10"]    = 0X28;
    boardUserDONameAddrMap["U_DO_11"]    = 0X29;
    boardUserDONameAddrMap["U_DO_12"]    = 0X2A;
    boardUserDONameAddrMap["U_DO_13"]    = 0X2B;
    boardUserDONameAddrMap["U_DO_14"]    = 0X2C;
    boardUserDONameAddrMap["U_DO_15"]    = 0X2D;
    boardUserDONameAddrMap["U_DO_16"]    = 0X2E;
    boardUserDONameAddrMap["U_DO_17"]    = 0X2F;   //User IO  DO



    /** 接口板 AI **/
    //Controller AI
    boardControllerAINameAddrMap.clear();

    //接口板用户AI
    boardUserAINameAddrMap.clear();
    boardUserAINameAddrMap["VI0"] = 0X00;
    boardUserAINameAddrMap["VI1"] = 0X01;
    boardUserAINameAddrMap["VI2"] = 0X02;
    boardUserAINameAddrMap["VI3"] = 0X03;





    /** 接口板 AO **/
    //Controller AO
    boardControllerAONameAddrMap.clear();

    //接口板用户AO
    boardUserAONameAddrMap.clear();
    boardUserAONameAddrMap["VO0"] = 0X00;
    boardUserAONameAddrMap["VO1"] = 0X01;
    boardUserAONameAddrMap["CO0"] = 0X02;
    boardUserAONameAddrMap["CO1"] = 0X03;

    m_boardIOMaping.clear();
    m_boardIOMaping[RobotBoardControllerDI]  =  boardControllerDINameAddrMap;
    m_boardIOMaping[RobotBoardControllerDO]  =  boardControllerDONameAddrMap;
    m_boardIOMaping[RobotBoardControllerAI]  =  boardControllerAINameAddrMap;
    m_boardIOMaping[RobotBoardControllerAO]  =  boardControllerAONameAddrMap;
    m_boardIOMaping[RobotBoardUserDI]        =  boardUserDINameAddrMap;
    m_boardIOMaping[RobotBoardUserDO]        =  boardUserDONameAddrMap;
    m_boardIOMaping[RobotBoardUserAI]        =  boardUserAINameAddrMap;
    m_boardIOMaping[RobotBoardUserAO]        =  boardUserAONameAddrMap;




    /** 接口板IO类型集合 **/
    m_boardIoTypeSet.clear();
    for(map<RobotIoType, map<string, int> >::iterator it=m_boardIOMaping.begin(); it!=m_boardIOMaping.end(); ++it)
    {
        m_boardIoTypeSet.insert(it->first);
    }


    /**  接口板user DI 地址和名称集合 **/
    m_boardUserDINameSet.clear();
    m_boardUserDIAddrSet.clear();
    for( map<string, int>::iterator it= boardUserDINameAddrMap.begin(); it!=boardUserDINameAddrMap.end(); ++it)
    {
        m_boardUserDINameSet.insert(it->first);
        m_boardUserDIAddrSet.insert(it->second);
    }
    /**  接口板user DO 地址和名称集合 **/
    m_boardUserDONameSet.clear();
    m_boardUserDOAddrSet.clear();
    for( map<string, int>::iterator it= boardUserDONameAddrMap.begin(); it!=boardUserDONameAddrMap.end(); ++it)
    {
        m_boardUserDONameSet.insert(it->first);
        m_boardUserDOAddrSet.insert(it->second);
    }
    /**  接口板user AI 地址和名称集合 **/
    m_boardUserAINameSet.clear();
    m_boardUserAIAddrSet.clear();
    for( map<string, int>::iterator it= boardUserAINameAddrMap.begin(); it!=boardUserAINameAddrMap.end(); ++it)
    {
        m_boardUserAINameSet.insert(it->first);
        m_boardUserAIAddrSet.insert(it->second);
    }
    /**  接口板user AO 地址和名称集合 **/
    m_boardUserAONameSet.clear();
    m_boardUserAOAddrSet.clear();
    for(map<string, int>::iterator it=boardUserAONameAddrMap.begin(); it!=boardUserAONameAddrMap.end(); ++it)
    {
        m_boardUserAONameSet.insert(it->first);
        m_boardUserAOAddrSet.insert(it->second);
    }



    /**  接口板Controller DI 地址和名称集合 **/
    m_boardControllerDINameSet.clear();
    m_boardControllerDIAddrSet.clear();
    for( map<string, int>::iterator it= boardControllerDINameAddrMap.begin(); it!=boardControllerDINameAddrMap.end(); ++it)
    {
        m_boardControllerDINameSet.insert(it->first);
        m_boardControllerDIAddrSet.insert(it->second);
    }
    /**  接口板Controller DO 地址和名称集合 **/
    m_boardControllerDONameSet.clear();
    m_boardControllerDOAddrSet.clear();
    for( map<string, int>::iterator it= boardControllerDONameAddrMap.begin(); it!=boardControllerDONameAddrMap.end(); ++it)
    {
        m_boardControllerDONameSet.insert(it->first);
        m_boardControllerDOAddrSet.insert(it->second);
    }
    /**  接口板Controller AI 地址和名称集合 **/
    m_boardControllerAINameSet.clear();
    m_boardControllerAIAddrSet.clear();
    for( map<string, int>::iterator it= boardControllerAINameAddrMap.begin(); it!=boardControllerAINameAddrMap.end(); ++it)
    {
        m_boardControllerAINameSet.insert(it->first);
        m_boardControllerAIAddrSet.insert(it->second);
    }
    /**  接口板Controller AO 地址和名称集合 **/
    m_boardControllerAONameSet.clear();
    m_boardControllerAOAddrSet.clear();
    for(map<string, int>::iterator it=boardControllerAONameAddrMap.begin(); it!=boardControllerAONameAddrMap.end(); ++it)
    {
        m_boardControllerAONameSet.insert(it->first);
        m_boardControllerAOAddrSet.insert(it->second);
    }

    m_boardIOTypeAddrSetMaping.clear();
    m_boardIOTypeAddrSetMaping[RobotBoardControllerDI]  =  m_boardControllerDIAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardControllerDO]  =  m_boardControllerDOAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardControllerAI]  =  m_boardControllerAIAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardControllerAO]  =  m_boardControllerAOAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardUserDI]        =  m_boardUserDIAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardUserDO]        =  m_boardUserDOAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardUserAI]        =  m_boardUserAIAddrSet;
    m_boardIOTypeAddrSetMaping[RobotBoardUserAO]        =  m_boardUserAOAddrSet;

    m_boardIOTypeNameSetMaping.clear();
    m_boardIOTypeNameSetMaping[RobotBoardControllerDI]  =  m_boardControllerDINameSet;
    m_boardIOTypeNameSetMaping[RobotBoardControllerDO]  =  m_boardControllerDONameSet;
    m_boardIOTypeNameSetMaping[RobotBoardControllerAI]  =  m_boardControllerAINameSet;
    m_boardIOTypeNameSetMaping[RobotBoardControllerAO]  =  m_boardControllerAONameSet;
    m_boardIOTypeNameSetMaping[RobotBoardUserDI]        =  m_boardUserDINameSet;
    m_boardIOTypeNameSetMaping[RobotBoardUserDO]        =  m_boardUserDONameSet;
    m_boardIOTypeNameSetMaping[RobotBoardUserAI]        =  m_boardUserAINameSet;
    m_boardIOTypeNameSetMaping[RobotBoardUserAO]        =  m_boardUserAONameSet;

}

void RobotIoService::initToolIoConfig()
{
    m_toolPowerMaping.clear();
    m_toolPowerMaping["T_POWER"]  = 0x00;

    /** 工具端 Diagnosis IO **/
    m_toolDiagnosisIONameAddrMaping.clear();
    m_toolDiagnosisIONameAddrMaping["T_DI/O_00"] = TOOL_DIGITAL_IO_0;
    m_toolDiagnosisIONameAddrMaping["T_DI/O_01"] = TOOL_DIGITAL_IO_1;
    m_toolDiagnosisIONameAddrMaping["T_DI/O_02"] = TOOL_DIGITAL_IO_2;
    m_toolDiagnosisIONameAddrMaping["T_DI/O_03"] = TOOL_DIGITAL_IO_3;


    m_toolDiagnosisIOAddrNameMaping[TOOL_DIGITAL_IO_0]="T_DI/O_00";
    m_toolDiagnosisIOAddrNameMaping[TOOL_DIGITAL_IO_1]="T_DI/O_01";
    m_toolDiagnosisIOAddrNameMaping[TOOL_DIGITAL_IO_2]="T_DI/O_02";
    m_toolDiagnosisIOAddrNameMaping[TOOL_DIGITAL_IO_3]="T_DI/O_03";

    /** 工具端 AI **/
    m_toolAIMaping.clear();
    m_toolAIMaping["T_AI_00"] = 0x00;
    m_toolAIMaping["T_AI_01"] = 0x01;

    m_toolDiagnosisIOAddrSet.clear();
    for( map<string, int>::iterator it= m_toolDiagnosisIONameAddrMaping.begin(); it!=m_toolDiagnosisIONameAddrMaping.end(); ++it)
    {
        m_toolDiagnosisIOAddrSet.insert(it->second);
    }
}
