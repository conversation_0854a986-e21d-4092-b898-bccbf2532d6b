#include "hanweiservices.h"

#include <math.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <string.h>
#include <unistd.h>
#include <math.h>

#include "globalutil.h"
#include "robotutilservice.h"
#include "protoencodedecode.h"
#include "robotcontrolservices.h"

using namespace aubo_robot_namespace;


//#define HANWEI_CUSTOM_MADE 1

//std::string HanWeiServices::s_version = "V4.0.8";

std::string HanWeiServices::s_loginName = "hanwei";

HanWeiServices::HanWeiServices(RobotControlServices *p)
{
    m_robotBaseService = p;

    safeLimitParamInit();

    m_wayPointVector.clear();
    m_splitStartIndex.clear();
}

int HanWeiServices::safeLimitParamInit()
{
    double minJointLimit[6]; double maxJointLimit[6];

    int ret = ErrnoSucc;

    //获取失败最多重试3次
    for(int i=0; i<3;i++)
    {
        if( (ret = getMovePJointRangeService(minJointLimit, maxJointLimit)) == ErrnoSucc )
        {
            break;
        }
    }

    if(ret == aubo_robot_namespace::ErrnoSucc)
    {
        for(int i=0;i<6;i++)
        {
            m_jointRangeMinLimit[i] = minJointLimit[i];
            m_jointRangeMaxLimit[i] = maxJointLimit[i];
        }

        for(int i=0;i<6;i++)
        {
            W_INFO("MoveP joint range: %d [%.2f, %.2f]", i+1, m_jointRangeMinLimit[i]*180.0/M_PI, m_jointRangeMaxLimit[i]*180.0/M_PI);
        }
    }
    else
    {
        W_ERROR("sdk log: Get movep joint range failed.");

        #ifdef HANWEI_CUSTOM_MADE
        m_jointRangeMinLimit[0] = -50.0/180.0*M_PI;
        m_jointRangeMaxLimit[0] =  45.0/180.0*M_PI;

        m_jointRangeMinLimit[1] = -40.0/180.0*M_PI;
        m_jointRangeMaxLimit[1] =  50.0/180.0*M_PI;

        m_jointRangeMinLimit[2] = -155.0/180.0*M_PI;
        m_jointRangeMaxLimit[2] = -20.0/180.0*M_PI;

        m_jointRangeMinLimit[3] = -174.5/180.0*M_PI;
        m_jointRangeMaxLimit[3] =  50.0/180.0*M_PI;

        m_jointRangeMinLimit[4] = -147.0/180.0*M_PI;
        m_jointRangeMaxLimit[4] =  50.0/180.0*M_PI;

        m_jointRangeMinLimit[5] = -130.0/180.0*M_PI;
        m_jointRangeMaxLimit[5] =  130.0/180.0*M_PI;
        #else
        for(int i=0; i<6; i++)
        {
            m_jointRangeMinLimit[i] = -174.0/180.0*M_PI;
            m_jointRangeMaxLimit[i] =  174.0/180.0*M_PI;
        }
        #endif
    }

    // \NOTE(louwei): MOVEP运动模式下，限制关节角度没有意义，这里留下这个宏定义兼容HANWEI版本
#ifdef HANWEI_CUSTOM_MADE
    //路点之间 最大距离限制
    m_maxDistDiffLimit = 12.0;   //mm

    //路点之间 最大关节角限制
    m_maxjointAngleDiffLimit[0] = 8*M_PI;
    m_maxjointAngleDiffLimit[1] = 8*M_PI;
    m_maxjointAngleDiffLimit[2] = 8*M_PI;
    m_maxjointAngleDiffLimit[3] = 8*M_PI;
    m_maxjointAngleDiffLimit[4] = 8*M_PI;
    m_maxjointAngleDiffLimit[5] = 30*M_PI;
#else
    //路点之间 最大距离限制
    m_maxDistDiffLimit = 100;   //mm

    //路点之间 最大关节角限制(180°附近)
    m_maxjointAngleDiffLimit[0] = 0.9999*M_PI;
    m_maxjointAngleDiffLimit[1] = 0.9999*M_PI;
    m_maxjointAngleDiffLimit[2] = 0.9999*M_PI;
    m_maxjointAngleDiffLimit[3] = 0.9999*M_PI;
    m_maxjointAngleDiffLimit[4] = 0.9999*M_PI;
    m_maxjointAngleDiffLimit[5] = 0.9999*M_PI;
#endif

    return ret;
}

int HanWeiServices::getMovePJointRangeService(double minJointLimit[], double maxJointLimit[])
{
    int   ret;

    CommunicationResponse robotResponse;

    // 做实时版本与非实时兼容
    if (m_robotBaseService->getVersionCode() > 4010000) {
        ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getMovePJointRangeLimitParam_V4010000, NULL, 0, robotResponse);
    } else {
        ret = aubo_robot_namespace::ErrCode_Failed;
        //ret = m_robotBaseService->requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getMovePJointRangeLimitParam, NULL, 0, robotResponse);
    }

    if(ret == aubo_robot_namespace::ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<double> doubleTypeValueVector;

        if(ProtoEncodeDecode::resolveResponse_doubleTypeValueVector(robotResponse.m_textPtr,robotResponse.m_textLength, doubleTypeValueVector, errorCode)==true)
        {
            if((ret=m_robotBaseService->getErrCodeByServerResponse(errorCode)) == aubo_robot_namespace::ErrnoSucc)
            {
                if(doubleTypeValueVector.size()==12)
                {
                    for(int i=0;i<6;i++)
                    {
                        minJointLimit[i] = doubleTypeValueVector.at(i);
                        maxJointLimit[i] = doubleTypeValueVector.at(6+i);
                    }
                }
                else
                {
                    ret = aubo_robot_namespace::ErrCode_ResolveResponseFailed;
                }
            }
        }
        else
        {
            ret = aubo_robot_namespace::ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve getMovePJointRange response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int HanWeiServices::setMovePJointRangeService(const double minJointLimit[], const double maxJointLimit[])
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;
    std::vector<double> doubleTypeValueVector;

    for(int i=0;i<6;i++)
    {
        doubleTypeValueVector.push_back(minJointLimit[i]);
    }

    for(int i=0;i<6;i++)
    {
        doubleTypeValueVector.push_back(maxJointLimit[i]);
    }

    if( ProtoEncodeDecode::getRequest_doubleTypeValueVector(&protobufTextPtr, &protobufTextLength, doubleTypeValueVector) == true )
    {
        // 做实时版本与非实时兼容
        if (m_robotBaseService->getVersionCode() > 4010000) {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setMovePJointRangeLimitParam_V4010000, protobufTextPtr, protobufTextLength);
        } else {
            ret = m_robotBaseService->requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setMovePJointRangeLimitParam, protobufTextPtr, protobufTextLength);
        }
    }
    else
    {
        ret = aubo_robot_namespace::ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setMovePJointRangeLimitParam protobuf content failed.");
    }

    return ret;
}




void HanWeiServices::setJointRangeLimit(const double *jointMinLimit, const double *jointMaxLimit)
{
    for(int i=0; i<6; i++)
    {
        m_jointRangeMinLimit[i] = jointMinLimit[i];
        m_jointRangeMaxLimit[i] = jointMaxLimit[i];
    }
}

bool HanWeiServices::isMeetJointRangeLimit(const double *jointData)
{
    bool ret = true;

    for(int i=0; i<6; i++)
    {
        if( jointData[i] < m_jointRangeMinLimit[i] || jointData[i] > m_jointRangeMaxLimit[i])
        {
            ret = false;
            W_ERROR("Joint angle not meet limit range. id[1-6][%.3f,%.3f]:%d  val:%.3f",
                    i+1, m_jointRangeMinLimit[i]*180.0/M_PI, m_jointRangeMaxLimit[i]*180.0/M_PI,
                    jointData[i]*180.0/M_PI);
            break;
        }
    }

    return ret;
}

bool HanWeiServices::isMeetJointAngleDiffLimit(const double *jointGroup1, const double *jointGroup2)
{
    bool ret = true;

    for(int i=0; i<6; i++)
    {
        if( fabs( jointGroup1[i] - jointGroup2[i] ) > m_maxjointAngleDiffLimit[i])
        {
            W_ERROR("JointAngleDiffLimit NG:[ID(1-6)]:%d %.2f, %.2f, %.2f", i+1, jointGroup1[i]*180.0/M_PI, jointGroup2[i]*180.0/M_PI, fabs( jointGroup1[i] - jointGroup2[i] )*180.0/M_PI );
            ret = false;
            break;
        }
    }

    return ret;
}

bool HanWeiServices::isMeetDistDiffLimit(const double *jointGroup1, const double *jointGroup2)
{
    bool ret = false;

    aubo_robot_namespace::wayPoint_S wayPoint1, wayPoint2;

    if( RobotUtilService::robotFk(jointGroup1, 6, wayPoint1) == aubo_robot_namespace::ErrnoSucc &&
        RobotUtilService::robotFk(jointGroup2, 6, wayPoint2) == aubo_robot_namespace::ErrnoSucc )
    {
        ret = isMeetDistDiffLimit(wayPoint1.cartPos.position, wayPoint2.cartPos.position);
    }

    return ret;
}

bool HanWeiServices::isMeetDistDiffLimit(const aubo_robot_namespace::Pos &position1, const aubo_robot_namespace::Pos &position2)
{
    //return ( sqrt( pow( position1.x-position2.x, 2) + pow( position1.y-position2.y, 2) + pow( position1.z-position2.z, 2) )*1000.0 < m_maxDistDiffLimit);

    if( sqrt( pow( position1.x-position2.x, 2) + pow( position1.y-position2.y, 2) + pow( position1.z-position2.z, 2) )*1000.0 < m_maxDistDiffLimit)
    {
        return true;
    }
    else
    {
        W_ERROR("position1:%.3f %.3f %.3f  position2:%.3f %.3f %.3f  ", position1.x, position1.y, position1.z, position2.x, position2.y, position2.z);

        W_ERROR("Dist diff:%.3f", sqrt( pow( position1.x-position2.x, 2) + pow( position1.y-position2.y, 2) + pow( position1.z-position2.z, 2) )*1000.0);

        return false;
    }
}

bool HanWeiServices::safetyFenceLimit(const aubo_robot_namespace::Pos &position1)
{
    bool ret = false;

    const double flat_X_Min =  0.15;  //单位:米
    const double flat_X_Max =  0.70;  //单位:米
    const double flat_Y_Min = -0.60;  //单位:米
    const double flat_Y_Max =  0.60;  //单位:米
    const double flat_Z     = -0.50;  //单位:米

    aubo_robot_namespace::Pos position = position1;

    //空间点(x, y, z) 绕Y轴旋转   让坐标系水平
    codeRotateByY(position.x, position.z, 45.0, position.x, position.z);

    if( !(position.x > flat_X_Min && position.x < flat_X_Max) ||
        !(position.y > flat_Y_Min && position.y < flat_Y_Max) ||
        position.z < flat_Z)
    {
        ret = false;
    }

    return ret;
}

bool HanWeiServices::isMeetLimit(const std::vector<HanWeiServices::FlangeAndToolInfo> &flangeAndToolInfoVector)
{
    bool ret = true;

    //对是否满足关节范围限制进行判断
    for(size_t i=0; i<flangeAndToolInfoVector.size(); i++)
    {
        if(isMeetJointRangeLimit(flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos) == false)
        {
            W_ERROR("Does not meet the joint range limit, index:%d joint:%.3f %.3f %.3f %.3f %.3f %.3f", i,
                    flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos[0]*180.0/M_PI,
                    flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos[1]*180.0/M_PI,
                    flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos[2]*180.0/M_PI,
                    flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos[3]*180.0/M_PI,
                    flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos[4]*180.0/M_PI,
                    flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos[5]*180.0/M_PI);

            return false;
        }
    }

    //对路点间的距离和角度  差进行判断
    for(size_t i=1; i<flangeAndToolInfoVector.size(); i++)
    {
        if(isMeetJointAngleDiffLimit(flangeAndToolInfoVector.at(i-1).flangeWayPoint.jointpos, flangeAndToolInfoVector.at(i).flangeWayPoint.jointpos) == false)
        {
            W_ERROR("Does not meet the joint angle diff limit, index:%d %d", i-1, i);

            return false;
        }

        if(isMeetDistDiffLimit(flangeAndToolInfoVector.at(i-1).endPosition, flangeAndToolInfoVector.at(i).endPosition) == false)
        {
            W_ERROR("tool point cannot meet the dist limit, index:%d %d", i-1, i);

            return false;
        }
#ifdef HANWEI_CUSTOM_MADE
        if(isMeetDistDiffLimit(flangeAndToolInfoVector.at(i-1).flangeWayPoint.cartPos.position, flangeAndToolInfoVector.at(i).flangeWayPoint.cartPos.position) == false)
        {
            W_ERROR("flange wayPoint cannot meet the dist limit, index:%d %d", i-1, i);

            return false;
        }
#endif
    }

    return ret;
}

const std::vector<HanWeiServices::FlangeAndToolInfo> &HanWeiServices::getFlangeAndToolInfoVector()
{
    return m_wayPointVector;
}

std::vector<int> &HanWeiServices::getSplitIndexVector()
{
    return m_splitStartIndex;
}

int HanWeiServices::parseFileAsRoadpointSet(const char *filePath, POSITION_ORIENTATION_TYPE poseType,
                                            const double *referPointJointAngle, const ToolInEndDesc &toolInEndDesc,
                                            std::vector<HanWeiServices::FlangeAndToolInfo> &roadpointInfoVector)
{
    int ret = ErrnoSucc;

    Rpy rpy;                                 //欧拉角
    Ori quaternion;                          //四元数
    Pos toolEndPosition;                     //工具末端位置
    Ori toolEndQuaternion;                   //工具末端姿态
    FlangeAndToolInfo lastFlangeAndToolInfo; //连续逆解时 参考点

    roadpointInfoVector.clear();

    //初始化上一个点的法兰路点信息:对参考点进行正解
    if ( (ret = RobotUtilService::robotFk(referPointJointAngle, 6, lastFlangeAndToolInfo.flangeWayPoint)) != aubo_robot_namespace::ErrnoSucc )
    {
        W_ERROR("sdk log: reference point fk failed");
        return ret;
    }

    //初始化上一个点的工具末端点位置信息 基转基加工具  得到工具的位置和姿态
    ret = RobotUtilService::base2BaseAdditionalTool(lastFlangeAndToolInfo.flangeWayPoint.cartPos.position, lastFlangeAndToolInfo.flangeWayPoint.orientation,
                                                    toolInEndDesc, lastFlangeAndToolInfo.endPosition, lastFlangeAndToolInfo.endOrientation);
    if(ret != aubo_robot_namespace::ErrnoSucc)
    {
        W_ERROR("sdk log: reference point baseToBaseAdditionalTool failed");
        return ret;
    }

    std::string line;
    std::ifstream in(filePath);

    if(!in)
    {
        W_ERROR("sdk log: track file was not found, path=%s", filePath);   // 没有该文件
        ret = aubo_robot_namespace::ErrCode_ParamError;
    }

    int lineIndex = 0;
    //逐行读取文件
    while (ret == aubo_robot_namespace::ErrnoSucc && getline (in, line) )
    {
        lineIndex++;

        //将字符串 转换为 位置+欧拉角
        if(poseType == POSITION_MM_AND_RPY_ANGLE_SPACE_SPLIT)
        {
            if(StringToPositionAndRpy(line, ' ',toolEndPosition, rpy) == false)
            {
                ret = ErrCode_Failed;

                W_ERROR("sdk log: StringToPositionAndRpy failed, lineIndex = %d", lineIndex);

                continue;
            }

            if( (ret = RobotUtilService::RPYToQuaternion(rpy, quaternion)) != aubo_robot_namespace::ErrnoSucc)
            {
                W_ERROR("sdk log: RPYToQuaternion failed, lineIndex = %d", lineIndex);

                continue;
            }
        }
        //将字符串 转换为 位置+四元数
        else if(poseType == POSITION_M_AND_QUATERNION_COMMA_SPLIT)
        {
            if(StringToPositionAndQuaternion(line, ',', toolEndPosition, quaternion) == false)
            {
                ret = aubo_robot_namespace::ErrCode_Failed;

                W_ERROR("StringToPositionAndQuaternion failed, lineIndex = %d", lineIndex);

                continue;
            }
        }

        //逆解运算
        if((ret = RobotUtilService::robotServiceIkBasedOnTcp(lastFlangeAndToolInfo.flangeWayPoint.jointpos, toolEndPosition, quaternion, toolInEndDesc, lastFlangeAndToolInfo.flangeWayPoint))!= aubo_robot_namespace::ErrnoSucc)
        {
            W_ERROR("sdk log: Tcp ik failed. lineIndex:%d", lineIndex);
        }

        //对参考点赋值
        lastFlangeAndToolInfo.endPosition     = toolEndPosition;
        lastFlangeAndToolInfo.endOrientation  = toolEndQuaternion;

        roadpointInfoVector.push_back(lastFlangeAndToolInfo);
    }

    if(ret == aubo_robot_namespace::ErrnoSucc && roadpointInfoVector.size()>0)
    {
        W_INFO("Parse the track file as a collection of waypoints, filePath:%s roadpointCount:%d", filePath, roadpointInfoVector.size());
    }

    return ret;
}

int HanWeiServices::parsePoseListToRoadpointSet(const std::vector<aubo_robot_namespace::PositionAndQuaternion> &toolEndPoseVector, const double *referPointJointAngle, const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, std::vector<FlangeAndToolInfo> &roadpointInfoVector)
{
    int ret = ErrnoSucc;

    FlangeAndToolInfo lastFlangeAndToolInfo;

    roadpointInfoVector.clear();

    //初始化上一个点的法兰路点信息:对参考点进行正解
    if ( (ret = RobotUtilService::robotFk(referPointJointAngle, 6, lastFlangeAndToolInfo.flangeWayPoint)) != aubo_robot_namespace::ErrnoSucc )
    {
        W_ERROR("sdk log: reference point fk failed");

        return ret;
    }

    //初始化上一个点的工具末端点位置信息 基转基加工具  得到工具的位置和姿态
    ret = RobotUtilService::base2BaseAdditionalTool(lastFlangeAndToolInfo.flangeWayPoint.cartPos.position, lastFlangeAndToolInfo.flangeWayPoint.orientation,
                                                    toolInEndDesc, lastFlangeAndToolInfo.endPosition, lastFlangeAndToolInfo.endOrientation);
    if(ret != aubo_robot_namespace::ErrnoSucc)
    {
        W_ERROR("sdk log: reference point baseToBaseAdditionalTool failed");
        return ret;
    }

    //逐行读取文件
    for(size_t index=0; ret == ErrnoSucc && index < toolEndPoseVector.size(); index++)
    {
        //逆解运算
        if((ret = RobotUtilService::robotServiceIkBasedOnTcp(lastFlangeAndToolInfo.flangeWayPoint.jointpos, toolEndPoseVector.at(index).position,
                                                             toolEndPoseVector.at(index).quaternion, toolInEndDesc, lastFlangeAndToolInfo.flangeWayPoint))!= aubo_robot_namespace::ErrnoSucc)
        {
            W_ERROR("sdk log: Tcp ik failed. index:%d", index+1);
        }

        //对参考点赋值
        lastFlangeAndToolInfo.endPosition     = toolEndPoseVector.at(index).position;
        lastFlangeAndToolInfo.endOrientation  = toolEndPoseVector.at(index).quaternion;

        roadpointInfoVector.push_back(lastFlangeAndToolInfo);
    }

    if(ret != aubo_robot_namespace::ErrnoSucc )
    {
        roadpointInfoVector.clear();
    }

    return ret;
}

void HanWeiServices::trackFilter(std::vector<HanWeiServices::FlangeAndToolInfo> &flangeAndToolInfoVector)
{
    //二阶低通滤波
    double Coe[5];
    std::vector<double> jointAgnleVector;
    commonUtil::iir_low_filter_Init(12.0, 200.0, 0.668, Coe);  //20 200

    double *dat_o = (double*)malloc( sizeof (double) * flangeAndToolInfoVector.size() );

    for(int joint_i=0; joint_i<6; joint_i++)
    {
        jointAgnleVector.clear();

        for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
        {
            jointAgnleVector.push_back(flangeAndToolInfoVector.at(index).flangeWayPoint.jointpos[joint_i]);
        }

        commonUtil::iir_low_filter_run(&jointAgnleVector[0], dat_o, Coe,flangeAndToolInfoVector.size());

        for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
        {
           flangeAndToolInfoVector[index].flangeWayPoint.jointpos[joint_i] = dat_o[index];
        }
    }
    free(dat_o);

    //对滤波后点进行正解
    for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
    {
        RobotUtilService::robotFk(flangeAndToolInfoVector[index].flangeWayPoint.jointpos, 6, flangeAndToolInfoVector[index].flangeWayPoint);
    }
}

void HanWeiServices::trackSplit(const std::vector<FlangeAndToolInfo> &flangeAndToolInfoVector, std::vector<int> &split_start_index)
{
    #define TURN_BACK_SPLIT_THR 0.00001

    int ret = aubo_robot_namespace::ErrnoSucc;

    FlangeAndToolInfo lastDistanceValidWp[2];
    int lastDistanceValidWpIdx=0;
    double currDistanceValidVect[3], currDistanceValidNorm, lastDistanceValidVect[3]={0}, lastDistanceValidNorm=0;

    double dist2;

    for( size_t index=0; ret == aubo_robot_namespace::ErrnoSucc && index<flangeAndToolInfoVector.size(); index++ )
    {
        if (index == 0 )
        {
            lastDistanceValidWpIdx = 0;
            lastDistanceValidWp[lastDistanceValidWpIdx&1] = flangeAndToolInfoVector[index];
            lastDistanceValidWpIdx++;
            split_start_index.clear();
            split_start_index.push_back(index);
        }
        else
        {
            currDistanceValidNorm = 0;
            currDistanceValidVect[0] = (flangeAndToolInfoVector[index].endPosition.x - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.x);
            currDistanceValidVect[1] = (flangeAndToolInfoVector[index].endPosition.y - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.y);
            currDistanceValidVect[2] = (flangeAndToolInfoVector[index].endPosition.z - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.z);
            for (int i=0;i<3;i++) currDistanceValidNorm += currDistanceValidVect[i]*currDistanceValidVect[i];
            currDistanceValidNorm = sqrt(currDistanceValidNorm);
            if (currDistanceValidNorm > TURN_BACK_SPLIT_THR && currDistanceValidNorm > 0)
            {
                if (lastDistanceValidWpIdx > 1)
                {
                    dist2 = 0;
                    for (int i=0;i<3;i++) dist2 += currDistanceValidVect[i]*lastDistanceValidVect[i];
                    dist2 /= currDistanceValidNorm*lastDistanceValidNorm;
                    //cosd(1/2/5) = 0.999848/0.999391/0.996195/0.984808
                    if (dist2 < - 0.996195)
                    {
                        lastDistanceValidWp[lastDistanceValidWpIdx&1] = lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1];
                        split_start_index.push_back(index-1);
                        lastDistanceValidWpIdx = 1; //start from last wp.
                        W_DEBUG("movep split indx: %d", index-1);
                    }
                }

                lastDistanceValidWp[lastDistanceValidWpIdx&1] = flangeAndToolInfoVector[index];
                lastDistanceValidWpIdx++;
                memcpy(lastDistanceValidVect,currDistanceValidVect,sizeof(double)*3);
                lastDistanceValidNorm = currDistanceValidNorm;
            }
        }
    }
    split_start_index.push_back(flangeAndToolInfoVector.size()-1);
}


int HanWeiServices::makeTrackByFileAndSmoothHandle(const char *filePath, POSITION_ORIENTATION_TYPE poseType, const double *referPointJointAngle,
                                                   const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, std::vector<HanWeiServices::FlangeAndToolInfo> &wayPointVector)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    wayPointVector.clear();

    //setp: 位姿文件 转换为 路点容器
    if( (ret = parseFileAsRoadpointSet(filePath, poseType, referPointJointAngle, toolInEndDesc, wayPointVector)) != ErrnoSucc)
    {
        W_ERROR("Parse the track file as a collection waypoints failed, filePath:%s", filePath);
    }

    std::vector<int> splitPointIndex;
    ret = trackSmoothAndSafeCheck(wayPointVector, true, true, false, splitPointIndex);

    return ret;
}

int HanWeiServices::makeTrackByFileAndSmoothSplitHandle(const char *filePath, POSITION_ORIENTATION_TYPE poseType, const double *referPointJointAngle, aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, aubo_robot_namespace::wayPoint_S &firstWayPoint)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    m_wayPointVector.clear();
    m_splitStartIndex.clear();

    //setp: 位姿文件 转换为 路点容器
    if( (ret = parseFileAsRoadpointSet(filePath, poseType, referPointJointAngle, toolInEndDesc, m_wayPointVector)) != ErrnoSucc)
    {
        W_ERROR("Parse the track file as a collection waypoints failed, filePath:%s", filePath);
    }

    std::vector<int> splitPointIndex;
    ret = trackSmoothAndSafeCheck(m_wayPointVector, true, true, true, m_splitStartIndex);

    //异常判断
    if(ret == aubo_robot_namespace::ErrnoSucc)
    {
        firstWayPoint = m_wayPointVector.at(0).flangeWayPoint;
    }
    else
    {
        m_wayPointVector.clear();
        m_splitStartIndex.clear();
    }

    return ret;
}

int HanWeiServices::makeTrackByPoseList(const double *referPointJointAngle, const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
                                        const std::vector<aubo_robot_namespace::PositionAndQuaternion> &toolEndPoseVector, std::vector<aubo_robot_namespace::wayPoint_S> &wayPointVector)
{
    int  ret = 0;

    std::vector<FlangeAndToolInfo> flangeAndToolInfoVector;

    ret = parsePoseListToRoadpointSet(toolEndPoseVector, referPointJointAngle, toolInEndDesc, flangeAndToolInfoVector);

    if(ret != aubo_robot_namespace::ErrnoSucc)
    {
        W_ERROR("Convert pose list to waypoint vector failed.");
    }

    std::vector<int> splitPointIndex;
    ret = trackSmoothAndSafeCheck(flangeAndToolInfoVector, true, true, false, splitPointIndex);

    return ret;
}



int HanWeiServices::trackSmoothAndSafeCheck(std::vector<HanWeiServices::FlangeAndToolInfo> &wayPointVector, bool isSafeCheck, bool isSmoothFilter, bool isSplit, std::vector<int> &splitPointIndex)
{
    int ret = ErrnoSucc;

    //step: 对逆解后的轨迹进行“限制条件”判断
    if(isSafeCheck && isMeetLimit(wayPointVector)==false)
    {
        ret = ErrCode_Failed;

        W_ERROR("Trajectory before filtering does not meet the limit.");
    }

    //step: 滤波处理
    if(ret == ErrnoSucc && isSmoothFilter)
    {
        trackFilter(wayPointVector);
    }

    //step: 对滤波后的轨迹进行“限制条件”判断
    if(ret == ErrnoSucc && isSafeCheck && isMeetLimit(wayPointVector)==false)
    {
        ret = ErrCode_Failed;

        W_ERROR("Trajectory filted does not meet the limit.");
    }

    //路点数量判断
    if(ret == ErrnoSucc && wayPointVector.size()<3)
    {
        ret = ErrCode_MotionWaypointVetorSizeError;
    }

    //step 轨迹分割处理
    if(ret == ErrnoSucc)
    {
        if(isSplit)
        {
            trackSplit(wayPointVector, splitPointIndex);
        }
        else
        {
            splitPointIndex.clear();
            splitPointIndex.push_back(0);
            splitPointIndex.push_back( wayPointVector.size()-1 );
        }
    }

    return ret;
}

void HanWeiServices::codeRotateByZ(double x, double y, double thetaz, double &outx, double &outy)
{
    double x1 = x;   //将变量拷贝一次，保证&x == &outx这种情况下也能计算正确

    double y1 = y;

    double rz = thetaz * M_PI / 180;

    outx = cos(rz) * x1 - sin(rz) * y1;

    outy = sin(rz) * x1 + cos(rz) * y1;
}


void HanWeiServices::codeRotateByY(double x, double z, double thetay, double &outx, double &outz)
{
    double x1 = x;

    double z1 = z;

    double ry = thetay * M_PI / 180.0;

    outx = cos(ry) * x1 + sin(ry) * z1;

    outz = cos(ry) * z1 - sin(ry) * x1;
}


void HanWeiServices::codeRotateByX(double y, double z, double thetax, double &outy, double &outz)
{
    double y1 = y;//将变量拷贝一次，保证&y == &y这种情况下也能计算正确

    double z1 = z;

    double rx = thetax * M_PI / 180;

    outy = cos(rx) * y1 - sin(rx) * z1;

    outz = cos(rx) * z1 + sin(rx) * y1;
}

bool HanWeiServices::StringToPositionAndRpy(const std::string &str, char splitSymbol, Pos &position, Rpy &rpy)
{
    std::string ss;

    std::vector<double> doubleVector;

    std::string str1 = commonUtil::removeSurplusSpaces(str);   //移除字符串中多余的空格

    std::istringstream tmpStringstream(str1);

    while (getline(tmpStringstream, ss, splitSymbol))
    {
        doubleVector.push_back( atof(ss.c_str()) );
    }

    if(doubleVector.size() == 6)
    {
        position.x = doubleVector[0]/1000.0;
        position.y = doubleVector[1]/1000.0;
        position.z = doubleVector[2]/1000.0;

        rpy.rx = doubleVector[3]/180.0*M_PI;
        rpy.ry = doubleVector[4]/180.0*M_PI;
        rpy.rz = doubleVector[5]/180.0*M_PI;

        return true;
    }
    else
    {
        W_ERROR("StringToPositionAndRpy failed, doubleVector.size=%d", doubleVector.size());
    }

    return false;
}

bool HanWeiServices::StringToPositionAndQuaternion(const std::string &str, char splitSymbol, Pos &position, Ori &quaternion)
{
    std::string ss;

    std::istringstream tmp_string(str);

    std::vector<double> doubleVector;

    while (getline(tmp_string, ss, splitSymbol))
    {
        doubleVector.push_back( atof(ss.c_str()) );
    }

    if(doubleVector.size() == 7)
    {
        position.x   = doubleVector[0];
        position.y   = doubleVector[1];
        position.z   = doubleVector[2];

        quaternion.w = doubleVector[3];
        quaternion.x = doubleVector[4];
        quaternion.y = doubleVector[5];
        quaternion.z = doubleVector[6];

        return true;
    }
    else
    {
        W_ERROR("StringToPositionAndQuaternion failed, doubleVector.size=%d", doubleVector.size());
    }

    return false;
}

